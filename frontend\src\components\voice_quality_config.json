{"voice_cloning": {"vad_enabled": true, "vad_threshold": 0.5, "enhancement_method": "spectral", "noise_reduction_factor": 2.5, "embedding_normalization": true, "reference_duration_min": 10, "reference_duration_max": 30, "sample_rate": 24000}, "tone_conversion": {"tau_ultra": 0.3, "tau_high": 0.4, "tau_standard": 0.5, "message": "@OpenVoiceV2", "device_optimization": true, "fp16_inference": false}, "tts_generation": {"noise_scale": 0.333, "noise_scale_w": 0.6, "length_scale": 1.0, "temperature": 0.3, "top_k": 20, "top_p": 0.9, "voice_stability": 0.85, "emotion_strength": 0.8}, "audio_enhancement": {"spectral_gating": true, "noise_floor_percentile": 10, "gate_threshold_multiplier": 2.5, "smoothing_factor": 0.95, "compression_ratio": 0.8, "final_normalization": true}, "quality_presets": {"ultra": {"description": "Maximum quality, slower processing", "tau": 0.3, "enhancement": "spectral", "iterations": 2, "batch_size": 1}, "high": {"description": "High quality, balanced speed", "tau": 0.4, "enhancement": "spectral", "iterations": 1, "batch_size": 1}, "standard": {"description": "Good quality, faster processing", "tau": 0.5, "enhancement": "basic", "iterations": 1, "batch_size": 2}}}