"""
OpenVoice V2 Model Downloader
Downloads required models for OpenVoice V2 voice cloning system
Based on the complete setup guide for proper voice cloning functionality
"""

import os
import sys
import requests
import zipfile
import torch
import subprocess
from pathlib import Path
import tempfile
import shutil

class OpenVoiceV2Downloader:
    """Complete OpenVoice V2 setup and model downloader"""
    
    def __init__(self, base_dir="./models/openvoice"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # Official model URLs from setup guide
        self.v2_checkpoint_url = "https://myshell-public-repo-hosting.s3.amazonaws.com/openvoice/checkpoints_v2_0417.zip"
        self.v1_backup_url = "https://myshell-public-repo-hosting.s3.amazonaws.com/checkpoints_1226.zip"
        
        print("🎵 OpenVoice V2 Downloader for Clonie")
        print("Superior voice cloning with 6 languages support")
        print("=" * 60)
    
    def check_system_requirements(self):
        """Check if system meets OpenVoice V2 requirements"""
        print("🔍 Checking system requirements...")
        
        # Check Python version
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        print(f"✅ Python {python_version}")
        
        # Check PyTorch
        try:
            import torch
            print(f"✅ PyTorch {torch.__version__}")
            
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name()
                vram_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                print(f"✅ CUDA available: {gpu_name}")
                print(f"✅ VRAM: {vram_gb:.1f} GB")
                
                if vram_gb < 4:
                    print("⚠️ Warning: Less than 4GB VRAM, may affect performance")
            else:
                print("⚠️ CUDA not available - will use CPU (slower)")
        except ImportError:
            print("❌ PyTorch not installed")
            return False
        
        # Check disk space
        free_space_gb = shutil.disk_usage('.').free / (1024**3)
        if free_space_gb < 10:
            print(f"⚠️ Low disk space: {free_space_gb:.1f} GB (recommend 10+ GB)")
        else:
            print(f"✅ Disk space: {free_space_gb:.1f} GB available")
        
        return True
    
    def download_with_progress(self, url, destination):
        """Download file with progress tracking"""
        print(f"📥 Downloading {destination.name}...")
        
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(destination, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            print(f"\r📊 Progress: {progress:.1f}%", end='', flush=True)
            
            print(f"\n✅ Downloaded {destination.name} ({downloaded / (1024*1024):.1f} MB)")
            return True
            
        except Exception as e:
            print(f"\n❌ Failed to download {destination.name}: {e}")
            return False
    
    def extract_models(self, zip_path, extract_to):
        """Extract model files with validation"""
        print(f"📦 Extracting {zip_path.name}...")
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
            
            print(f"✅ Extracted to {extract_to}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to extract {zip_path.name}: {e}")
            return False
    
    def verify_v2_models(self):
        """Verify OpenVoice V2 model files"""
        print("\n🔍 Verifying OpenVoice V2 models...")
        
        required_files = [
            self.base_dir / "checkpoints_v2" / "converter" / "config.json",
            self.base_dir / "checkpoints_v2" / "converter" / "checkpoint.pth",
            self.base_dir / "checkpoints_v2" / "base_speakers" / "ses" / "en-default.pth",
            self.base_dir / "checkpoints_v2" / "base_speakers" / "ses" / "es.pth",
            self.base_dir / "checkpoints_v2" / "base_speakers" / "ses" / "fr.pth",
            self.base_dir / "checkpoints_v2" / "base_speakers" / "ses" / "zh.pth",
            self.base_dir / "checkpoints_v2" / "base_speakers" / "ses" / "jp.pth",
            self.base_dir / "checkpoints_v2" / "base_speakers" / "ses" / "kr.pth",
        ]
        
        all_present = True
        total_size = 0
        
        for file_path in required_files:
            if file_path.exists():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                total_size += size_mb
                print(f"✅ {file_path.name} ({size_mb:.1f} MB)")
                
                # Verify PyTorch model files can be loaded
                if file_path.suffix == '.pth':
                    try:
                        torch.load(file_path, map_location='cpu')
                        print(f"   ✓ Model file verified")
                    except Exception as e:
                        print(f"   ❌ Model file corrupted: {e}")
                        all_present = False
            else:
                print(f"❌ Missing: {file_path.name}")
                all_present = False
        
        if all_present:
            print(f"\n🎯 All OpenVoice V2 models verified! Total size: {total_size:.1f} MB")
            return True
        else:
            print(f"\n⚠️ Some models are missing or corrupted")
            return False
    
    def install_openvoice_dependencies(self):
        """Install OpenVoice V2 specific dependencies"""
        print("\n📦 Installing OpenVoice V2 dependencies...")
        
        dependencies = [
            "librosa==0.9.1",
            "pydub==0.25.1", 
            "resampy>=0.2.2",
            "faster-whisper==0.9.0",
            "whisper-timestamped==1.14.2",
            "wavmark==0.0.3",
            "eng_to_ipa==0.0.2",
            "inflect==7.0.0",
            "unidecode==1.3.7",
            "pypinyin==0.50.0",
            "jieba==0.42.1",
            "cn2an",
            "gradio",
            "langid",
            "openai",
            "python-dotenv"
        ]
        
        for dep in dependencies:
            try:
                print(f"Installing {dep}...")
                subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                             check=True, capture_output=True)
                print(f"✅ Installed {dep}")
            except subprocess.CalledProcessError as e:
                print(f"⚠️ Failed to install {dep}: {e}")
        
        # Install MeloTTS for V2
        print("\n📦 Installing MeloTTS for OpenVoice V2...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", 
                          "git+https://github.com/myshell-ai/MeloTTS.git"], 
                         check=True, capture_output=True)
            print("✅ MeloTTS installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ Failed to install MeloTTS: {e}")
    
    def download_openvoice_v2(self):
        """Main download process for OpenVoice V2"""
        print("\n🚀 Starting OpenVoice V2 download...")
        
        # Download V2 checkpoints
        v2_zip_path = self.base_dir / "checkpoints_v2_0417.zip"
        
        if not v2_zip_path.exists():
            if not self.download_with_progress(self.v2_checkpoint_url, v2_zip_path):
                print("❌ Failed to download V2 checkpoints")
                return False
        else:
            print(f"✅ V2 checkpoints already downloaded")
        
        # Extract V2 models
        if not (self.base_dir / "checkpoints_v2").exists():
            if not self.extract_models(v2_zip_path, self.base_dir):
                print("❌ Failed to extract V2 models")
                return False
        else:
            print("✅ V2 models already extracted")
        
        # Verify installation
        if self.verify_v2_models():
            # Create success marker
            marker_file = self.base_dir / "openvoice_v2_ready.txt"
            with open(marker_file, 'w') as f:
                f.write("OpenVoice V2 models downloaded and verified successfully\n")
                f.write(f"GPU: {torch.cuda.get_device_name() if torch.cuda.is_available() else 'CPU'}\n")
                f.write("Languages: English, Spanish, French, Chinese, Japanese, Korean\n")
            
            print("\n🎉 OpenVoice V2 setup complete!")
            print("🚀 Ready for superior voice cloning!")
            return True
        else:
            print("\n❌ OpenVoice V2 setup failed")
            return False
    
    def clone_openvoice_repo(self):
        """Clone OpenVoice repository for integration"""
        repo_dir = self.base_dir / "OpenVoice"
        
        if not repo_dir.exists():
            print("\n📦 Cloning OpenVoice repository...")
            try:
                subprocess.run([
                    "git", "clone", "https://github.com/myshell-ai/OpenVoice.git", 
                    str(repo_dir)
                ], check=True, capture_output=True)
                print("✅ OpenVoice repository cloned")
                
                # Install in development mode
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "-e", str(repo_dir)
                ], check=True, capture_output=True)
                print("✅ OpenVoice installed in development mode")
                
            except subprocess.CalledProcessError as e:
                print(f"⚠️ Failed to clone/install OpenVoice: {e}")
                return False
        else:
            print("✅ OpenVoice repository already exists")
        
        return True

def main():
    """Main setup function"""
    downloader = OpenVoiceV2Downloader()
    
    if not downloader.check_system_requirements():
        print("\n❌ System requirements not met")
        return False
    
    # Install dependencies
    downloader.install_openvoice_dependencies()
    
    # Clone repository
    if not downloader.clone_openvoice_repo():
        print("\n⚠️ Repository setup failed, but continuing with model download...")
    
    # Download and setup models
    if downloader.download_openvoice_v2():
        print("\n🎉 OpenVoice V2 setup completed successfully!")
        print("🎵 Your voice cloning system is ready with 6 languages support!")
        return True
    else:
        print("\n❌ OpenVoice V2 setup failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
