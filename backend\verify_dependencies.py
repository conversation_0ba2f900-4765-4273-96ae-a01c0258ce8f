#!/usr/bin/env python3
"""
Dependency verification script for OpenVoice V2 Voice Cloning System
Checks if all required packages are installed with compatible versions
"""

import sys
import importlib.util

def check_package_version(package_name, required_version=None, min_version=None, max_version=None):
    """Check if a package is installed and meets version requirements"""
    try:
        module = importlib.import_module(package_name)
        version = getattr(module, '__version__', 'unknown')
        
        if required_version and version != required_version:
            return False, f"{package_name} {version} (required: {required_version})"
        elif min_version or max_version:
            from packaging import version as pkg_version
            v = pkg_version.parse(version)
            if min_version and v < pkg_version.parse(min_version):
                return False, f"{package_name} {version} (min required: {min_version})"
            if max_version and v >= pkg_version.parse(max_version):
                return False, f"{package_name} {version} (max allowed: {max_version})"
        
        return True, f"{package_name} {version} ✅"
    except ImportError:
        return False, f"{package_name} not installed ❌"
    except Exception as e:
        return False, f"{package_name} error: {e} ❌"

def main():
    """Main verification function"""
    print("🔍 Verifying OpenVoice V2 Dependencies...")
    print("=" * 50)
    
    # Critical dependencies for OpenVoice V2
    dependencies = [
        ("huggingface_hub", None, "0.34.0", None),
        ("tokenizers", None, "0.13.0", "0.15.0"),
        ("transformers", "4.27.4", None, None),
        ("librosa", "0.9.1", None, None),
        ("torch", None, "2.0.0", None),
        ("torchaudio", None, "2.0.0", None),
        ("faster_whisper", None, None, None),
        ("melo", None, None, None),  # MeloTTS imports as 'melo'
        ("MeCab", None, None, None),  # mecab-python3 imports as 'MeCab'
    ]
    
    all_good = True
    
    for package, required, min_ver, max_ver in dependencies:
        success, message = check_package_version(package, required, min_ver, max_ver)
        print(message)
        if not success:
            all_good = False
    
    print("=" * 50)
    
    if all_good:
        print("✅ All dependencies are compatible!")
        
        # Test OpenVoice V2 imports
        print("\n🔍 Testing OpenVoice V2 imports...")
        try:
            from openvoice_v2_cloning import _ensure_openvoice_imports
            if _ensure_openvoice_imports():
                print("✅ OpenVoice V2 imports successful!")
                return 0
            else:
                print("❌ OpenVoice V2 imports failed!")
                return 1
        except Exception as e:
            print(f"❌ OpenVoice V2 import test failed: {e}")
            return 1
    else:
        print("❌ Some dependencies have version conflicts!")
        print("\n💡 To fix conflicts, run:")
        print("   pip install \"tokenizers>=0.13,<0.15\" \"transformers==4.27.4\" \"librosa==0.9.1\"")
        return 1

if __name__ == "__main__":
    sys.exit(main())
