#!/usr/bin/env python3
"""
Test OpenVoice V2 System
Simple test to verify OpenVoice V2 is working correctly
"""

import sys
import traceback

def test_imports():
    """Test if OpenVoice V2 can be imported"""
    print("🧪 Testing OpenVoice V2 imports...")
    
    try:
        import openvoice_v2_cloning
        print("✅ OpenVoice V2 module imported successfully")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_initialization():
    """Test if OpenVoice V2 can be initialized"""
    print("\n🧪 Testing OpenVoice V2 initialization...")
    
    try:
        from openvoice_v2_cloning import OpenVoiceV2VoiceCloning
        
        print("⏳ Initializing OpenVoice V2 system...")
        system = OpenVoiceV2VoiceCloning()
        print("✅ OpenVoice V2 system initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        traceback.print_exc()
        return False

def test_main_py():
    """Test if main.py can be imported"""
    print("\n🧪 Testing main.py imports...")
    
    try:
        import main
        print("✅ main.py imported successfully")
        return True
    except Exception as e:
        print(f"❌ main.py import failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🔧 OpenVoice V2 System Test")
    print("=" * 40)
    
    results = {}
    
    # Run tests
    results['imports'] = test_imports()
    results['main'] = test_main_py()
    
    # Only test initialization if imports work
    if results['imports']:
        results['initialization'] = test_initialization()
    else:
        results['initialization'] = False
        print("⏭️ Skipping initialization test due to import failure")
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 20)
    
    passed = sum(1 for v in results.values() if v)
    total = len(results)
    
    for test, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test.upper():15} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! OpenVoice V2 system is ready!")
    else:
        print("⚠️ Some tests failed. Check the details above.")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS:")
        if not results['imports']:
            print("- Check if OpenVoice V2 dependencies are installed")
            print("- Run: python download_openvoice_v2.py")
        if not results['main']:
            print("- Check main.py for syntax errors")
        if not results['initialization']:
            print("- Ensure OpenVoice V2 models are downloaded")
            print("- Run: python download_openvoice_v2.py")

if __name__ == "__main__":
    main()
