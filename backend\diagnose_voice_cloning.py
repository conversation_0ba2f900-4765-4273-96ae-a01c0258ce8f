#!/usr/bin/env python3
"""
Voice Cloning System Diagnostics
Diagnose and fix common issues with voice cloning systems
"""

import os
import sys
import tempfile
import traceback
from pathlib import Path

def check_system_requirements():
    """Check if all required packages are installed"""
    print("🔍 Checking system requirements...")
    
    required_packages = [
        ('torch', 'PyTorch'),
        ('torchaudio', 'TorchAudio'),
        ('librosa', 'Librosa'),
        ('soundfile', 'SoundFile'),
        ('numpy', 'NumPy'),
        ('scipy', 'SciPy'),
    ]
    
    missing = []
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - MISSING")
            missing.append(package)
    
    if missing:
        print(f"\n📦 Install missing packages: pip install {' '.join(missing)}")
        return False
    
    return True

# XTTS v2 system removed - using OpenVoice V2 only

def check_openvoice_system():
    """Test OpenVoice V2 system"""
    print("\n🧪 Testing OpenVoice V2 system...")

    try:
        # Import without triggering initialization
        import openvoice_v2_cloning
        print("✅ OpenVoice V2 module imports successful")

        # Try to initialize (this will trigger the actual imports)
        print("⏳ Initializing OpenVoice V2 system...")
        system = openvoice_v2_cloning.OpenVoiceV2VoiceCloning()
        print("✅ OpenVoice V2 system initialized successfully")
        return True

    except Exception as e:
        print(f"❌ OpenVoice V2 system failed: {e}")
        print("📋 Full error:")
        traceback.print_exc()
        return False

def check_file_permissions():
    """Check file system permissions"""
    print("\n🔐 Checking file system permissions...")
    
    try:
        # Test temp file creation
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp:
            tmp_path = tmp.name
        
        # Test file operations
        with open(tmp_path, 'wb') as f:
            f.write(b'test')
        
        # Test file reading
        with open(tmp_path, 'rb') as f:
            data = f.read()
        
        # Cleanup
        os.unlink(tmp_path)
        
        print("✅ File system permissions OK")
        return True
        
    except Exception as e:
        print(f"❌ File system permission error: {e}")
        return False

def check_model_files():
    """Check if OpenVoice V2 model files exist"""
    print("\n📁 Checking OpenVoice V2 model files...")

    # Check OpenVoice V2 models
    openvoice_dir = Path(__file__).parent / "models" / "openvoice"
    checkpoints_dir = openvoice_dir / "checkpoints_v2"

    if checkpoints_dir.exists():
        print("✅ OpenVoice V2 model directory exists")

        # Check for key files
        converter_dir = checkpoints_dir / "converter"
        if converter_dir.exists():
            print("✅ OpenVoice V2 converter models found")
            return True
        else:
            print("⚠️ OpenVoice V2 converter models missing")
            print("💡 Run: python download_openvoice_v2.py")
            return False

    else:
        print("⚠️ OpenVoice V2 models not found")
        print("💡 Run: python download_openvoice_v2.py")
        return False

def test_audio_processing():
    """Test basic audio processing"""
    print("\n🎵 Testing audio processing...")
    
    try:
        import librosa
        import numpy as np
        import soundfile as sf
        
        # Create test audio
        sample_rate = 16000
        duration = 1.0  # 1 second
        frequency = 440  # A4 note
        
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio = np.sin(2 * np.pi * frequency * t)
        
        # Test file operations
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp:
            tmp_path = tmp.name
        
        # Write audio
        sf.write(tmp_path, audio, sample_rate)
        print("✅ Audio file writing OK")
        
        # Read audio
        audio_loaded, sr_loaded = librosa.load(tmp_path, sr=sample_rate)
        print("✅ Audio file reading OK")
        
        # Cleanup
        os.unlink(tmp_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Audio processing error: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("🔧 Voice Cloning System Diagnostics")
    print("=" * 50)
    
    results = {}
    
    # Run all checks
    results['requirements'] = check_system_requirements()
    results['permissions'] = check_file_permissions()
    results['audio'] = test_audio_processing()
    results['models'] = check_model_files()
    results['openvoice'] = check_openvoice_system()
    
    # Summary
    print("\n📊 DIAGNOSTIC SUMMARY")
    print("=" * 30)
    
    passed = sum(1 for v in results.values() if v)
    total = len(results)
    
    for test, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test.upper():15} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All systems operational!")
    else:
        print("⚠️ Some issues detected. Check the details above.")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS:")
        if not results['requirements']:
            print("- Install missing Python packages")
        if not results['permissions']:
            print("- Check file system permissions")
        if not results['audio']:
            print("- Verify audio processing libraries")
        if not results['openvoice']:
            print("- OpenVoice V2 system needs setup - run: python download_openvoice_v2.py")

if __name__ == "__main__":
    main()
