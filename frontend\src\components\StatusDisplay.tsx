import React from 'react';
import { Check<PERSON>ircle, AlertCircle, Refresh<PERSON><PERSON>, Loader2 } from 'lucide-react';

interface StatusDisplayProps {
  isReady: boolean;
  status: string;
  onRetry: () => void;
}

const StatusDisplay: React.FC<StatusDisplayProps> = ({ isReady, status, onRetry }) => {
  const isError = status.includes('not available') || status.includes('Error');
  const isLoading = status.includes('initializing') || status.includes('Checking');

  return (
    <div className={`rounded-lg p-4 flex items-center justify-between ${
      isError 
        ? 'bg-red-50 border border-red-200'
        : isReady 
        ? 'bg-green-50 border border-green-200'
        : 'bg-blue-50 border border-blue-200'
    }`}>
      <div className="flex items-center space-x-3">
        {isLoading ? (
          <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
        ) : isError ? (
          <AlertCircle className="w-5 h-5 text-red-600" />
        ) : isReady ? (
          <CheckCircle className="w-5 h-5 text-green-600" />
        ) : (
          <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
        )}
        
        <div>
          <h3 className={`font-medium ${
            isError ? 'text-red-800' : isReady ? 'text-green-800' : 'text-blue-800'
          }`}>
            System Status
          </h3>
          <p className={`text-sm ${
            isError ? 'text-red-700' : isReady ? 'text-green-700' : 'text-blue-700'
          }`}>
            {status}
          </p>
        </div>
      </div>

      {isError && (
        <button
          onClick={onRetry}
          className="btn-secondary flex items-center text-sm"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </button>
      )}
    </div>
  );
};

export default StatusDisplay;
