#!/bin/bash
echo "Installing pydub for better audio format support..."
cd backend
pip install pydub>=0.25.0
echo
echo "Pydub installed! This will help with WebM audio format conversion."
echo "You may also need to install FFmpeg for full format support."
echo
echo "To install FFmpeg:"
echo "Ubuntu/Debian: sudo apt install ffmpeg"
echo "macOS: brew install ffmpeg"
echo "Or download from https://ffmpeg.org/download.html"
echo
