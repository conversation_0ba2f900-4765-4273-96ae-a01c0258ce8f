#!/usr/bin/env python3
"""
OpenVoice V2 Voice Quality Optimizer
Fine-tune settings for maximum voice matching accuracy
"""

import json
from pathlib import Path

def create_optimal_config():
    """Create configuration for optimal voice quality"""
    
    config = {
        "voice_cloning": {
            "vad_enabled": True,
            "vad_threshold": 0.5,
            "enhancement_method": "spectral",
            "noise_reduction_factor": 2.5,
            "embedding_normalization": True,
            "reference_duration_min": 10,
            "reference_duration_max": 30,
            "sample_rate": 24000
        },
        "tone_conversion": {
            "tau_ultra": 0.3,
            "tau_high": 0.4,
            "tau_standard": 0.5,
            "message": "@OpenVoiceV2",
            "device_optimization": True,
            "fp16_inference": False  # Full precision for quality
        },
        "tts_generation": {
            "noise_scale": 0.333,
            "noise_scale_w": 0.6,
            "length_scale": 1.0,
            "temperature": 0.3,
            "top_k": 20,
            "top_p": 0.9,
            "voice_stability": 0.85,
            "emotion_strength": 0.8
        },
        "audio_enhancement": {
            "spectral_gating": True,
            "noise_floor_percentile": 10,
            "gate_threshold_multiplier": 2.5,
            "smoothing_factor": 0.95,
            "compression_ratio": 0.8,
            "final_normalization": True
        },
        "quality_presets": {
            "ultra": {
                "description": "Maximum quality, slower processing",
                "tau": 0.3,
                "enhancement": "spectral",
                "iterations": 2,
                "batch_size": 1
            },
            "high": {
                "description": "High quality, balanced speed",
                "tau": 0.4,
                "enhancement": "spectral",
                "iterations": 1,
                "batch_size": 1
            },
            "standard": {
                "description": "Good quality, faster processing",
                "tau": 0.5,
                "enhancement": "basic",
                "iterations": 1,
                "batch_size": 2
            }
        }
    }
    
    # Save configuration
    config_path = Path("voice_quality_config.json")
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Optimal configuration created: voice_quality_config.json")
    return config

def apply_optimal_settings():
    """Apply optimal settings to the system"""
    
    print("🎯 OpenVoice V2 Voice Quality Optimizer")
    print("=" * 50)
    
    # Create optimal configuration
    config = create_optimal_config()
    
    print("\n📊 Optimal Settings Applied:")
    print("\n🎙️ Voice Cloning:")
    print(f"  • VAD Enabled: {config['voice_cloning']['vad_enabled']}")
    print(f"  • Enhancement: {config['voice_cloning']['enhancement_method']}")
    print(f"  • Sample Rate: {config['voice_cloning']['sample_rate']} Hz")
    
    print("\n🎨 Tone Conversion:")
    print(f"  • Ultra Quality Tau: {config['tone_conversion']['tau_ultra']}")
    print(f"  • Device Optimization: {config['tone_conversion']['device_optimization']}")
    
    print("\n🔊 TTS Generation:")
    print(f"  • Noise Scale: {config['tts_generation']['noise_scale']}")
    print(f"  • Temperature: {config['tts_generation']['temperature']}")
    print(f"  • Voice Stability: {config['tts_generation']['voice_stability']}")
    
    print("\n💡 Recommendations for 90%+ Voice Matching:")
    print("  1. Use 10-30 second clear audio samples")
    print("  2. Select 'Gentle' enhancement level")
    print("  3. Use 'High Quality' mode")
    print("  4. Enable advanced settings in the UI")
    print("  5. Fine-tune voice stability (0.8-0.9)")
    print("  6. Adjust emotion strength (0.7-0.9)")
    
    print("\n✅ Configuration saved for enhanced backend")
    
    # Create a marker file to indicate optimization
    marker = Path("models/openvoice/.optimized")
    marker.parent.mkdir(parents=True, exist_ok=True)
    marker.touch()
    
    return True

if __name__ == "__main__":
    apply_optimal_settings()
