import React, { useState } from 'react';
import { Setting<PERSON>, ChevronDown, ChevronUp, Sparkles, Info } from 'lucide-react';

export interface AdvancedSettings {
  temperature: number;
  length_penalty: number;
  repetition_penalty: number;
  top_k: number;
  top_p: number;
  voice_stability: number;
  emotion_strength: number;
  noise_scale: number;
  noise_scale_w: number;
}

interface EnhancedAdvancedTuningProps {
  onSettingsChange: (settings: AdvancedSettings | null) => void;
  disabled?: boolean;
}

const EnhancedAdvancedTuning: React.FC<EnhancedAdvancedTuningProps> = ({ 
  onSettingsChange, 
  disabled = false 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [settings, setSettings] = useState<AdvancedSettings>({
    temperature: 0.3,
    length_penalty: 1.0,
    repetition_penalty: 5.0,
    top_k: 20,
    top_p: 0.9,
    voice_stability: 0.85,
    emotion_strength: 0.8,
    noise_scale: 0.333,
    noise_scale_w: 0.6,
  });
  
  const [preset, setPreset] = useState<'ultra' | 'balanced' | 'fast'>('ultra');

  const presets = {
    ultra: {
      name: 'Ultra Quality (90%+ Match)',
      description: 'Maximum voice matching accuracy',
      settings: {
        temperature: 0.25,
        length_penalty: 1.0,
        repetition_penalty: 5.0,
        top_k: 15,
        top_p: 0.85,
        voice_stability: 0.9,
        emotion_strength: 0.85,
        noise_scale: 0.3,
        noise_scale_w: 0.5,
      }
    },
    balanced: {
      name: 'Balanced (80% Match)',
      description: 'Good quality with faster generation',
      settings: {
        temperature: 0.3,
        length_penalty: 1.0,
        repetition_penalty: 5.0,
        top_k: 20,
        top_p: 0.9,
        voice_stability: 0.85,
        emotion_strength: 0.8,
        noise_scale: 0.333,
        noise_scale_w: 0.6,
      }
    },
    fast: {
      name: 'Fast (70% Match)',
      description: 'Quickest generation, acceptable quality',
      settings: {
        temperature: 0.4,
        length_penalty: 1.0,
        repetition_penalty: 3.0,
        top_k: 30,
        top_p: 0.95,
        voice_stability: 0.75,
        emotion_strength: 0.7,
        noise_scale: 0.4,
        noise_scale_w: 0.7,
      }
    }
  };

  const handleToggle = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    onSettingsChange(newExpanded ? settings : null);
  };

  const updateSetting = (key: keyof AdvancedSettings, value: number) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    if (isExpanded) {
      onSettingsChange(newSettings);
    }
  };

  const applyPreset = (presetKey: 'ultra' | 'balanced' | 'fast') => {
    const presetSettings = presets[presetKey].settings;
    setSettings(presetSettings);
    setPreset(presetKey);
    if (isExpanded) {
      onSettingsChange(presetSettings);
    }
  };

  const parameterInfo = {
    voice_stability: "Higher values preserve voice characteristics better (0.8-0.95 for best match)",
    emotion_strength: "Controls emotional expressiveness (0.7-0.9 recommended)",
    temperature: "Lower values = more consistent output (0.2-0.4 for quality)",
    noise_scale: "Affects voice texture (lower = cleaner)",
    top_k: "Limits vocabulary selection (15-30 optimal)",
    top_p: "Probability threshold (0.85-0.95 range)",
  };

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <button
        onClick={handleToggle}
        disabled={disabled}
        className="w-full px-4 py-3 bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100 transition-colors duration-200 flex items-center justify-between disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5 text-purple-600" />
          <span className="font-medium text-gray-900">Advanced Voice Tuning</span>
          <Sparkles className="w-4 h-4 text-yellow-500" />
        </div>
        <div className="flex items-center space-x-2">
          {isExpanded && (
            <span className="text-sm text-purple-600 font-medium">
              {preset === 'ultra' ? '90%+ Match' : preset === 'balanced' ? '80% Match' : '70% Match'}
            </span>
          )}
          {isExpanded ? (
            <ChevronUp className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronDown className="w-5 h-5 text-gray-500" />
          )}
        </div>
      </button>

      {isExpanded && (
        <div className="p-4 bg-gray-50 space-y-4">
          {/* Quality Presets */}
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <h4 className="font-medium text-gray-900 mb-3">Quality Presets</h4>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
              {Object.entries(presets).map(([key, preset]) => (
                <button
                  key={key}
                  onClick={() => applyPreset(key as 'ultra' | 'balanced' | 'fast')}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    key === preset ? 
                    'border-purple-500 bg-purple-50' : 
                    'border-gray-200 hover:border-gray-300 bg-white'
                  }`}
                >
                  <div className="font-medium text-sm">{preset.name}</div>
                  <div className="text-xs text-gray-500 mt-1">{preset.description}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Primary Controls */}
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              Primary Voice Controls
              <span className="ml-2 text-xs text-purple-600 font-normal">
                (Most impact on quality)
              </span>
            </h4>
            
            {/* Voice Stability */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  Voice Stability
                  <div className="group relative ml-1">
                    <Info className="w-3 h-3 text-gray-400 cursor-help" />
                    <div className="absolute bottom-full left-0 mb-2 w-64 p-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                      {parameterInfo.voice_stability}
                    </div>
                  </div>
                </label>
                <span className="text-sm text-gray-500">{settings.voice_stability.toFixed(2)}</span>
              </div>
              <input
                type="range"
                min="0.5"
                max="0.95"
                step="0.05"
                value={settings.voice_stability}
                onChange={(e) => updateSetting('voice_stability', parseFloat(e.target.value))}
                className="w-full accent-purple-600"
              />
            </div>

            {/* Emotion Strength */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  Emotion Strength
                  <div className="group relative ml-1">
                    <Info className="w-3 h-3 text-gray-400 cursor-help" />
                    <div className="absolute bottom-full left-0 mb-2 w-64 p-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                      {parameterInfo.emotion_strength}
                    </div>
                  </div>
                </label>
                <span className="text-sm text-gray-500">{settings.emotion_strength.toFixed(2)}</span>
              </div>
              <input
                type="range"
                min="0.5"
                max="1.0"
                step="0.05"
                value={settings.emotion_strength}
                onChange={(e) => updateSetting('emotion_strength', parseFloat(e.target.value))}
                className="w-full accent-purple-600"
              />
            </div>
          </div>

          {/* Advanced Controls */}
          <details className="bg-white rounded-lg border border-gray-200">
            <summary className="px-4 py-3 cursor-pointer hover:bg-gray-50 font-medium text-gray-900">
              Advanced Parameters
            </summary>
            <div className="p-4 space-y-3 border-t border-gray-200">
              {/* Temperature */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="text-sm font-medium text-gray-700">Temperature</label>
                  <span className="text-sm text-gray-500">{settings.temperature.toFixed(2)}</span>
                </div>
                <input
                  type="range"
                  min="0.1"
                  max="1.0"
                  step="0.05"
                  value={settings.temperature}
                  onChange={(e) => updateSetting('temperature', parseFloat(e.target.value))}
                  className="w-full accent-gray-600"
                />
              </div>

              {/* Noise Scale */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="text-sm font-medium text-gray-700">Noise Scale</label>
                  <span className="text-sm text-gray-500">{settings.noise_scale.toFixed(3)}</span>
                </div>
                <input
                  type="range"
                  min="0.1"
                  max="1.0"
                  step="0.033"
                  value={settings.noise_scale}
                  onChange={(e) => updateSetting('noise_scale', parseFloat(e.target.value))}
                  className="w-full accent-gray-600"
                />
              </div>

              {/* Top K */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="text-sm font-medium text-gray-700">Top K</label>
                  <span className="text-sm text-gray-500">{settings.top_k}</span>
                </div>
                <input
                  type="range"
                  min="5"
                  max="50"
                  step="5"
                  value={settings.top_k}
                  onChange={(e) => updateSetting('top_k', parseInt(e.target.value))}
                  className="w-full accent-gray-600"
                />
              </div>

              {/* Top P */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="text-sm font-medium text-gray-700">Top P</label>
                  <span className="text-sm text-gray-500">{settings.top_p.toFixed(2)}</span>
                </div>
                <input
                  type="range"
                  min="0.5"
                  max="1.0"
                  step="0.05"
                  value={settings.top_p}
                  onChange={(e) => updateSetting('top_p', parseFloat(e.target.value))}
                  className="w-full accent-gray-600"
                />
              </div>
            </div>
          </details>

          {/* Tips */}
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 border border-purple-200">
            <h4 className="font-medium text-purple-900 mb-2">Pro Tips for 90%+ Voice Match</h4>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>• Use "Ultra Quality" preset for best results</li>
              <li>• Voice Stability 0.85-0.95 preserves characteristics</li>
              <li>• Lower Temperature (0.2-0.3) for consistency</li>
              <li>• Clean reference audio is crucial</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedAdvancedTuning;