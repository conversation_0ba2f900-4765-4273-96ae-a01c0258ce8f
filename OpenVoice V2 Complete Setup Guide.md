# OpenVoice V2 Complete Setup Guide

OpenVoice V2 requires a comprehensive download and setup process involving multiple model components, dependencies, and configuration files. Unlike simpler TTS systems, OpenVoice V2 uses a **two-stage architecture separating base TTS generation from tone color conversion**, requiring both base speaker models and tone conversion models for full functionality.

The setup involves downloading approximately **2GB of V2 model checkpoints** plus additional MeloTTS models, with native support for **6 languages** (English, Spanish, French, Chinese, Japanese, Korean) and significantly improved audio quality over V1 through enhanced training strategies.

## Repository and model locations

**Primary GitHub Repository:**
- URL: `https://github.com/myshell-ai/OpenVoice`  
- Owner: MyShell-AI (MIT License)
- 32.5k+ stars, actively maintained

**Official Model Downloads:**
- **V2 Checkpoints**: `https://myshell-public-repo-hosting.s3.amazonaws.com/openvoice/checkpoints_v2_0417.zip`
- **V1 Checkpoints**: `https://myshell-public-repo-hosting.s3.amazonaws.com/checkpoints_1226.zip`
- **Backup Source**: HuggingFace `Alignment-Lab-AI/OpenVoice` (reliable alternative for V1 models)

**HuggingFace Repositories:**
- Official: `myshell-ai/OpenVoiceV2` (documentation only, redirects to external downloads)  
- Community: `Alignment-Lab-AI/OpenVoice` (complete V1 model files with Git LFS)

## Required model files and structure

OpenVoice V2 uses a **modular architecture** requiring multiple specialized models:

### V2 Directory Structure
```
checkpoints_v2/
├── converter/                   # Tone color converter (universal)
│   ├── config.json             # Converter configuration  
│   └── checkpoint.pth          # Converter model weights (~131MB)
└── base_speakers/              # Multi-language base TTS models
    └── ses/                     # Speaker embeddings directory
        ├── en-default.pth      # English speaker embedding
        ├── es.pth              # Spanish speaker embedding  
        ├── fr.pth              # French speaker embedding
        ├── zh.pth              # Chinese speaker embedding
        ├── jp.pth              # Japanese speaker embedding
        └── kr.pth              # Korean speaker embedding
```

### Model Component Functions
**Base Speaker Models**: Generate speech with specific prosodic characteristics (emotion, accent, rhythm) for each supported language. V2 includes native models for 6 languages versus V1's English/Chinese focus.

**Tone Color Converter**: Universal model using encoder-decoder architecture with normalizing flow to convert tone color characteristics from any source voice to match a target speaker. This is the core innovation enabling zero-shot voice cloning.

**Speaker Embeddings**: Language-specific embeddings stored in `ses/` directory that capture tone color characteristics for each supported language and accent variant.

## Dependencies and environment setup

### Python Environment Requirements
- **Python Version**: 3.9 (strictly required - 3.10+ may have compatibility issues)
- **Environment Manager**: Conda recommended for isolation

### Core Dependencies
```
# Audio processing
librosa==0.9.1                 # Audio loading and feature extraction
pydub==0.25.1                  # Audio manipulation  
soundfile                      # Audio file I/O
resampy>=0.2.2                 # Audio resampling

# Machine learning  
torch==1.13.1                  # PyTorch (specific version for compatibility)
torchvision==0.14.1            # PyTorch vision utils
torchaudio==0.13.1             # PyTorch audio processing
numpy==1.22.0                  # Numerical computing

# Speech processing
faster-whisper==0.9.0          # Speech recognition
whisper-timestamped==1.14.2    # Whisper with timestamps
wavmark==0.0.3                 # Audio watermarking

# Text processing
eng_to_ipa==0.0.2              # English to IPA conversion
inflect==7.0.0                 # English word inflection
unidecode==1.3.7               # Unicode to ASCII
pypinyin==0.50.0               # Chinese pinyin conversion
jieba==0.42.1                  # Chinese text segmentation
cn2an                          # Chinese number conversion

# Additional utilities  
gradio                          # Web interface
langid                          # Language identification
openai                          # OpenAI API support
python-dotenv                  # Environment variables
```

### V2-Specific Additional Requirements
**MeloTTS Integration**: V2 requires MeloTTS for multi-lingual synthesis
```bash
pip install git+https://github.com/myshell-ai/MeloTTS.git
python -m unidic download
```

### CUDA and GPU Requirements
- **CUDA Version**: 11.7-12.1 supported  
- **GPU Memory**: 4GB VRAM minimum, 8GB+ recommended for stable operation
- **GPU Compatibility**: NVIDIA GTX 1060 or better (compute capability 6.0+)
- **Installation**: `conda install pytorch==1.13.1 torchvision==0.14.1 torchaudio==0.13.1 pytorch-cuda=11.7 -c pytorch -c nvidia`

### System Dependencies
- **FFmpeg**: Required for audio processing (`sudo apt install ffmpeg` on Linux)
- **Build Tools**: GCC/Visual Studio Build Tools for compilation
- **Disk Space**: 10GB+ recommended (2GB models + dependencies + workspace)

## File sizes and verification

### Download Verification Data
- **V2 Checkpoint Zip**: ~2GB total
- **V1 Checkpoint Zip**: ~300MB total  
- **Tone Color Converter**: 131MB (`converter/checkpoint.pth`)
- **Individual Speaker Embeddings**: Varies by language (typically 10-50MB each)
- **MeloTTS Models**: ~500MB per language (auto-downloaded)

### Verification Methods
```python
import os
import torch

def verify_v2_setup():
    """Verify OpenVoice V2 installation and model files"""
    required_files = [
        'checkpoints_v2/converter/config.json',
        'checkpoints_v2/converter/checkpoint.pth',
        'checkpoints_v2/base_speakers/ses/en-default.pth',
        'checkpoints_v2/base_speakers/ses/es.pth',
        'checkpoints_v2/base_speakers/ses/fr.pth', 
        'checkpoints_v2/base_speakers/ses/zh.pth',
        'checkpoints_v2/base_speakers/ses/jp.pth',
        'checkpoints_v2/base_speakers/ses/kr.pth'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            try:
                if file_path.endswith('.pth'):
                    torch.load(file_path, map_location='cpu')
                print(f"✓ {file_path} verified")
            except Exception as e:
                print(f"✗ {file_path} corrupted: {e}")
        else:
            print(f"✗ {file_path} missing")
```

## Version differences and compatibility

### V1 vs V2 Architecture Changes

**OpenVoice V1**:
- Directory: `checkpoints/`
- Languages: English and Chinese base speakers
- Structure: Separate EN/ZH speaker directories with style variations
- Files: `en_default_se.pth`, `en_style_se.pth`, `zh_default_se.pth`
- Audio Quality: Standard quality with basic training strategy

**OpenVoice V2**:  
- Directory: `checkpoints_v2/`
- Languages: 6 native languages (EN, ES, FR, ZH, JP, KR)
- Structure: Unified `ses/` directory with language-specific embeddings
- Files: Single embedding per language (e.g., `en-default.pth`, `es.pth`)
- Audio Quality: **Enhanced quality through improved training strategy and data augmentation**

### Migration Considerations
- **Backward Compatibility**: V2 installation doesn't break V1 functionality
- **API Changes**: Parameters shift from `style`/`language` to unified `accent` parameter
- **Performance**: V2 requires more VRAM but delivers significantly better audio quality
- **Dependencies**: V2 adds MeloTTS requirement while maintaining OpenVoice core dependencies

## Complete download script structure

Based on the research, a comprehensive OpenVoice V2 download script should include:

### Script Components Required
```python
import os
import requests
import zipfile
import torch
import subprocess
import hashlib
from pathlib import Path

class OpenVoiceV2Downloader:
    def __init__(self, base_dir="./OpenVoice"):
        self.base_dir = Path(base_dir)
        self.v2_checkpoint_url = "https://myshell-public-repo-hosting.s3.amazonaws.com/openvoice/checkpoints_v2_0417.zip"
        self.v1_backup_url = "https://myshell-public-repo-hosting.s3.amazonaws.com/checkpoints_1226.zip"
        
    def setup_environment(self):
        """Create conda environment and install dependencies"""
        commands = [
            "conda create -n openvoice python=3.9 -y",
            "conda activate openvoice",
            "pip install torch==1.13.1 torchvision==0.14.1 torchaudio==0.13.1 --index-url https://download.pytorch.org/whl/cu117",
            "pip install git+https://github.com/myshell-ai/MeloTTS.git",
            "python -m unidic download"
        ]
        
    def download_with_progress(self, url, destination):
        """Download file with progress tracking"""
        # Implementation with progress bar
        
    def verify_download(self, file_path, expected_size=None):
        """Verify file integrity"""
        # Implementation with size/hash checking
        
    def extract_models(self, zip_path, extract_to):
        """Extract model files with error handling"""
        # Implementation with validation
        
    def install_openvoice(self):
        """Clone and install OpenVoice repository"""
        # Git clone and pip install -e .
        
    def download_all_models(self):
        """Main download orchestration"""
        # Download V2 checkpoints, extract, verify, fallback to alternatives
```

### Error Handling Requirements
- **Network failures**: Retry logic with exponential backoff
- **Disk space checks**: Verify available space before downloads
- **Checksum verification**: Validate downloaded files against expected hashes when available
- **Fallback sources**: Use HuggingFace alternatives if S3 downloads fail
- **CUDA detection**: Graceful fallback to CPU mode if GPU unavailable
- **Dependency conflicts**: Environment isolation and version pinning

### Progress Tracking Implementation
- **Download progress**: File-by-file progress bars with speed/ETA
- **Extraction progress**: Zip extraction progress for large model files  
- **Verification status**: Clear success/failure indicators for each component
- **Setup validation**: Final system test to confirm complete installation

The script should create a **complete, verified OpenVoice V2 installation** with all models, dependencies, and configurations properly set up for immediate voice cloning use, similar to your existing StyleTTS2 downloader but adapted for OpenVoice V2's more complex multi-component architecture.

## Conclusion

OpenVoice V2 represents a significant advancement in voice cloning technology with its **modular architecture separating content generation from tone color conversion**. The setup process requires careful orchestration of multiple model downloads, dependency management, and verification steps, but results in a powerful system supporting 6 languages with superior audio quality compared to V1.

The key success factors for a robust download script are **comprehensive error handling, alternative download sources, and thorough verification** of the two-stage model architecture that makes OpenVoice V2's zero-shot voice cloning capabilities possible.