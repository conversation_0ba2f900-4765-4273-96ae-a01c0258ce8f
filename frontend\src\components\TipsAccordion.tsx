import React, { useState } from 'react';
import { Ch<PERSON>ronDown, ChevronUp, Lightbulb, Mic, Settings, FileAudio } from 'lucide-react';

const TipsAccordion: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const tips = [
    {
      icon: <FileAudio className="w-5 h-5" />,
      title: "Audio Quality Tips",
      items: [
        "Use clear audio recordings without background noise",
        "Upload at least 10-30 seconds of speech",
        "High-quality WAV files work best",
        "Avoid heavily compressed audio formats when possible"
      ]
    },
    {
      icon: <Settings className="w-5 h-5" />,
      title: "Enhancement Levels",
      items: [
        "None: Pure cloning - preserves exact voice characteristics",
        "Minimal: Only removes sub-50Hz frequencies",
        "Gentle: Minimal + very light cleanup",
        "Use 'None' for highest fidelity preservation"
      ]
    },
    {
      icon: <Mic className="w-5 h-5" />,
      title: "Text-to-Speech Best Practices",
      items: [
        "Keep text under 500 words per generation for optimal quality",
        "Use proper punctuation for natural speech rhythm",
        "High quality mode provides better results but takes longer",
        "Remove silence option helps with long pauses"
      ]
    }
  ];

  return (
    <div className="card">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between text-left"
      >
        <div className="flex items-center space-x-3">
          <Lightbulb className="w-6 h-6 text-yellow-500" />
          <h3 className="text-lg font-semibold text-gray-800">
            Tips for Best Quality
          </h3>
        </div>
        {isOpen ? (
          <ChevronUp className="w-5 h-5 text-gray-500" />
        ) : (
          <ChevronDown className="w-5 h-5 text-gray-500" />
        )}
      </button>

      {isOpen && (
        <div className="mt-6 space-y-6">
          {tips.map((section, index) => (
            <div key={index} className="border-l-4 border-blue-200 pl-4">
              <div className="flex items-center space-x-2 mb-3">
                <div className="text-blue-600">{section.icon}</div>
                <h4 className="font-medium text-gray-800">{section.title}</h4>
              </div>
              <ul className="space-y-2">
                {section.items.map((item, itemIndex) => (
                  <li key={itemIndex} className="text-sm text-gray-600 flex items-start">
                    <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          ))}

          <div className="bg-blue-50 rounded-lg p-4 mt-6">
            <h4 className="font-medium text-blue-800 mb-2">About OpenVoice V2</h4>
            <p className="text-sm text-blue-700">
              This voice cloning system uses OpenVoice V2 for superior voice synthesis:
            </p>
            <ul className="text-sm text-blue-700 mt-2 space-y-1">
              <li><strong>6 Languages:</strong> Native support for English, Spanish, French, Chinese, Japanese, Korean</li>
              <li><strong>Two-Stage Architecture:</strong> Advanced tone color conversion for better quality</li>
              <li><strong>Zero-Shot Cross-Lingual:</strong> Clone voices across different languages</li>
              <li><strong>Commercial Use:</strong> MIT Licensed - completely free for commercial use</li>
            </ul>
            <p className="text-sm text-blue-600 mt-2">
              OpenVoice V2 focuses on preserving original voice characteristics while maintaining
              excellent audio quality and supporting multiple languages natively.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TipsAccordion;
