#!/usr/bin/env python3
"""
Quick fix to add missing /health endpoint to main.py
"""

import sys

def fix_main_py():
    """Add health endpoint to main.py"""
    print("🔧 Quick Fix for OpenVoice V2 Backend")
    print("=" * 40)
    
    try:
        # Read current main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if health endpoint already exists
        if '@app.get("/health")' in content:
            print("✅ Health endpoint already exists!")
            return True
        
        # Find where to insert the health endpoint (after root endpoint)
        root_endpoint = '@app.get("/")'
        if root_endpoint not in content:
            print("❌ Could not find root endpoint in main.py")
            return False
        
        # Find the end of root endpoint function
        root_index = content.find(root_endpoint)
        # Find the next @app decorator or end of file
        next_decorator_index = content.find('@app.', root_index + 1)
        if next_decorator_index == -1:
            next_decorator_index = len(content)
        
        # Find the last closing brace before next decorator
        insert_index = content.rfind('}', root_index, next_decorator_index) + 1
        
        # Health endpoint code
        health_endpoint = '''

@app.get("/health")
async def health_check():
    """Health check endpoint for frontend"""
    return {
        "status": "healthy",
        "message": "OpenVoice V2 API is running!",
        "device": device,
        "tone_converter_loaded": tone_color_converter is not None,
        "tts_models_loaded": len(tts_models),
        "languages_available": list(LANGUAGE_MAP.keys())
    }
'''
        
        # Insert the health endpoint
        new_content = content[:insert_index] + health_endpoint + content[insert_index:]
        
        # Write back to file
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ Added /health endpoint to main.py")
        print("🚀 Please restart the backend server")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing main.py: {e}")
        return False

if __name__ == "__main__":
    success = fix_main_py()
    sys.exit(0 if success else 1)
