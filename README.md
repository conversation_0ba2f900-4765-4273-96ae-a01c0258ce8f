# Clonie - OpenVoice V2 Voice Cloning Web App

A modern web application for high-fidelity voice cloning and text-to-speech synthesis using OpenVoice V2, built with React frontend and Python backend.

## Project Structure

```
clonie/
├── frontend/          # React + Vite frontend
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/           # Python FastAPI backend
│   ├── openvoice_v2_cloning.py  # OpenVoice V2 implementation
│   ├── main.py        # FastAPI server
│   ├── download_openvoice_v2.py # Model downloader
│   └── requirements.txt
└── README.md
```

## Features

- **High-Fidelity Voice Cloning**: Uses OpenVoice V2 for superior voice synthesis
- **6 Languages Support**: Native support for English, Spanish, French, Chinese, Japanese, Korean
- **Two-Stage Architecture**: Advanced tone color conversion for better quality
- **Zero-Shot Cross-Lingual**: Clone voices across different languages
- **Modern Web Interface**: React-based UI with responsive design
- **Real-time Audio Processing**: Upload, clone, and generate speech seamlessly
- **Commercial Use**: MIT Licensed - completely free for commercial use
- **Enhancement Levels**: None, minimal, or gentle audio processing options

## Technology Stack

### Frontend
- React 18 with TypeScript
- Vite for fast development and building
- Modern CSS with responsive design
- Audio visualization and playback controls

### Backend
- Python with FastAPI
- OpenVoice V2 and MeloTTS for voice synthesis
- High-quality audio processing with librosa
- RESTful API design

## Getting Started

### Quick Start (Recommended)
```bash
# On Windows
start-clonie.bat

# On Linux/Mac
./start-clonie.sh
```

### Manual Setup

#### Backend Setup
```bash
cd backend
pip install -r requirements.txt
python download_openvoice_v2.py  # Download OpenVoice V2 models
python main.py
```
Backend will be available at: http://localhost:8000

#### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```
Frontend will be available at: http://localhost:5173

### First Run Notes
- First run requires downloading OpenVoice V2 models (~500MB)
- Run `python download_openvoice_v2.py` to download models
- Model initialization takes 1-2 minutes
- Requires ~1-2GB RAM for model loading
- GPU acceleration used automatically if CUDA available

## Original Features Preserved

All functionality from the original Gradio application has been maintained:
- Voice sample upload and processing
- Enhancement level selection (none/minimal/gentle)
- Quality mode selection (high/standard)
- Text-to-speech generation
- Audio download capabilities
- Real-time status updates

## Development Status

This is a conversion of the original Python/Gradio application to a modern web stack while preserving all core functionality and focusing on improved user experience.
