# Clonie Testing Guide

## Quick Start

### Option 1: Use the Startup Script (Recommended)
```bash
# On Windows
start-clonie.bat

# On Linux/Mac
./start-clonie.sh
```

### Option 2: Manual Start

#### Start Backend (Terminal 1)
```bash
cd backend
pip install -r requirements.txt
python main.py
```

#### Start Frontend (Terminal 2)
```bash
cd frontend
npm install
npm run dev
```

## Testing Checklist

### ✅ System Initialization
- [ ] Backend starts without errors on http://localhost:8000
- [ ] Frontend starts without errors on http://localhost:5173
- [ ] System status shows "System ready for voice cloning"
- [ ] Health check endpoint works: http://localhost:8000/health

### ✅ Voice Cloning Functionality
- [ ] **File Upload**: Can upload audio files (WAV, MP3, M4A)
- [ ] **Drag & Drop**: Can drag and drop audio files
- [ ] **File Validation**: Rejects non-audio files and files > 50MB
- [ ] **Enhancement Levels**: Can select None/Minimal/Gentle
- [ ] **Voice Cloning**: Successfully clones voice and generates sample
- [ ] **Sample Playback**: Can play the generated voice sample
- [ ] **Sample Download**: Can download the voice sample

### ✅ Text-to-Speech Functionality
- [ ] **Text Input**: Can enter text in the textarea
- [ ] **Word Count**: Shows word count and warnings for >500 words
- [ ] **Quality Modes**: Can select High/Standard quality
- [ ] **Remove Silence**: Toggle works correctly
- [ ] **Speech Generation**: Successfully generates speech from text
- [ ] **Audio Playback**: Can play generated speech
- [ ] **Audio Download**: Can download generated speech
- [ ] **Clear Function**: Clear button empties text field

### ✅ User Interface
- [ ] **Responsive Design**: Works on desktop, tablet, and mobile
- [ ] **Loading States**: Shows spinners during processing
- [ ] **Error Handling**: Displays helpful error messages
- [ ] **Status Updates**: Shows progress and completion messages
- [ ] **Tips Section**: Expandable tips accordion works
- [ ] **Visual Feedback**: Buttons and interactions provide feedback

### ✅ Feature Parity with Original
- [ ] **Same Voice Quality**: Output matches original Python app
- [ ] **Same Enhancement Options**: All three levels work identically
- [ ] **Same Processing**: Audio processing preserves quality
- [ ] **Same Workflow**: Two-step process (clone → generate)
- [ ] **Same Settings**: All original options available

## Expected Behavior

### Voice Cloning Process
1. Upload audio file (10-30 seconds recommended)
2. Select enhancement level (None recommended for best quality)
3. Click "Clone Voice" - should take 30-60 seconds
4. Voice sample plays automatically
5. Voice info displays duration, sample rate, processing type

### TTS Generation Process
1. Enter text (under 500 words recommended)
2. Select quality mode (High recommended)
3. Optionally enable silence removal
4. Click "Generate Speech" - should take 15-45 seconds
5. Generated audio plays and can be downloaded

## Troubleshooting

### Backend Issues
- **Port 8000 in use**: Change port in `backend/main.py`
- **Missing dependencies**: Run `pip install -r requirements.txt`
- **CUDA errors**: System will fall back to CPU automatically
- **Model download**: First run downloads XTTS v2 model (~1GB)

### Frontend Issues
- **Port 5173 in use**: Vite will automatically use next available port
- **CORS errors**: Check backend CORS settings in `main.py`
- **Build errors**: Run `npm install` to ensure dependencies

### Audio Issues
- **No audio playback**: Check browser audio permissions
- **Download fails**: Check if backend is serving files correctly
- **Poor quality**: Try different enhancement levels or audio input

## Performance Notes

- **First run**: Model download and initialization takes 2-5 minutes
- **Voice cloning**: 30-60 seconds depending on hardware
- **TTS generation**: 15-45 seconds depending on text length and quality mode
- **Memory usage**: ~2-4GB RAM for model loading
- **GPU acceleration**: Automatically used if CUDA available

## Browser Compatibility

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## File Format Support

### Input Audio
- WAV (recommended)
- MP3
- M4A
- Maximum size: 50MB

### Output Audio
- WAV format
- 24kHz sample rate
- 16-bit depth
