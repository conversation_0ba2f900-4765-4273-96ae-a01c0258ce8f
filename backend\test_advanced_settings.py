#!/usr/bin/env python3
"""
Test script to verify OpenVoice V2 advanced settings functionality
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from openvoice_v2_cloning import OpenVoiceV2VoiceCloning

def test_advanced_settings():
    """Test the advanced settings functionality"""
    print("🧪 Testing OpenVoice V2 Advanced Settings...")
    
    try:
        # Initialize the system
        print("🚀 Initializing OpenVoice V2 system...")
        voice_system = OpenVoiceV2VoiceCloning()
        print("✅ System initialized successfully!")
        
        # Test advanced settings structure
        test_settings = {
            'temperature': 0.25,
            'length_penalty': 1.0,
            'repetition_penalty': 10.0,
            'top_k': 50,
            'top_p': 0.85,
            'voice_stability': 0.8,
            'emotion_strength': 0.7,
            'noise_scale': 0.667,
            'noise_scale_w': 0.8
        }
        
        print("🔧 Testing advanced settings structure...")
        print(f"   Temperature: {test_settings['temperature']}")
        print(f"   Voice Stability: {test_settings['voice_stability']}")
        print(f"   Emotion Strength: {test_settings['emotion_strength']}")
        print(f"   Noise Scale: {test_settings['noise_scale']}")
        print(f"   Noise Scale W: {test_settings['noise_scale_w']}")
        print("✅ Advanced settings structure is valid!")
        
        # Test TTS generation method signature (without actual generation)
        print("🎯 Testing TTS method signature...")
        try:
            # This should not fail due to method signature issues
            result = voice_system.generate_tts(
                text="Test",
                quality_mode="high",
                remove_silence=False,
                language="english",
                speed=1.0,
                advanced_settings=test_settings
            )
            # We expect this to fail because no voice is cloned yet, but the method should accept the parameters
            print("✅ TTS method accepts advanced settings correctly!")
        except Exception as e:
            if "Please clone a voice first" in str(e):
                print("✅ TTS method signature is correct (expected 'no voice' error)")
            else:
                print(f"❌ Unexpected error: {e}")
                return False
        
        print("\n🎉 All advanced settings tests passed!")
        print("🚀 OpenVoice V2 is ready for ultra-high quality voice cloning!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_advanced_settings()
    sys.exit(0 if success else 1)
