#!/bin/bash

echo ""
echo "╔═══════════════════════════════════════════════════════════════╗"
echo "║                    Starting Clonie Web App                   ║"
echo "╠═══════════════════════════════════════════════════════════════╣"
echo "║   🎙️  OpenVoice V2 Voice Cloning Studio                     ║"
echo "║   🚀  React Frontend + Python FastAPI Backend               ║"
echo "║   📜  MIT Licensed - Free for commercial use                 ║"
echo "╚═══════════════════════════════════════════════════════════════╝"
echo ""

echo "📦 Installing backend dependencies..."
cd backend
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ Failed to install backend dependencies"
    exit 1
fi

echo ""
echo "📥 Checking OpenVoice V2 models..."
if [ ! -d "models/openvoice/checkpoints_v2/converter" ]; then
    echo "📥 Downloading OpenVoice V2 models..."
    python download_openvoice_v2.py
    if [ $? -ne 0 ]; then
        echo "❌ Failed to download OpenVoice V2 models"
        exit 1
    fi
else
    echo "✅ OpenVoice V2 models already downloaded"
fi

echo ""
echo "📦 Installing frontend dependencies..."
cd ../frontend
npm install
if [ $? -ne 0 ]; then
    echo "❌ Failed to install frontend dependencies"
    exit 1
fi

echo ""
echo "🚀 Starting backend server..."
cd ../backend
python main.py &
BACKEND_PID=$!

echo ""
echo "⏳ Waiting for backend to initialize..."
sleep 5

echo ""
echo "🌐 Starting frontend development server..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ Clonie is running!"
echo ""
echo "📱 Frontend: http://localhost:5173"
echo "🔧 Backend API: http://localhost:8000"
echo ""
echo "💡 Press Ctrl+C to stop both servers"
echo ""

# Wait for user interrupt
trap "echo ''; echo '🛑 Stopping servers...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo '👋 Clonie stopped.'; exit 0" INT

# Keep script running
wait
