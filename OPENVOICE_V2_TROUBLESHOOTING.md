# OpenVoice V2 Troubleshooting Guide

## 🚨 Common Issues & Solutions

### Issue 1: "Failed to clone voice" with File Not Found Error

**Symptoms:**
- Error: `[WinError 2] The system cannot find the file specified`
- Voice cloning fails during speaker embedding extraction
- VAD (Voice Activity Detection) processing fails

**Root Cause:**
- File path issues in OpenVoice V2 speaker embedding extraction
- Temporary file cleanup issues
- Missing model files

**Solutions:**

1. **Download OpenVoice V2 Models:**
   ```bash
   cd backend
   python download_openvoice_v2.py
   ```

2. **Run Diagnostics:**
   ```bash
   cd backend
   python diagnose_voice_cloning.py
   ```

3. **Check Audio File:**
   - Use clear WAV files (10-30 seconds)
   - Ensure good audio quality
   - Verify file is not corrupted

### Issue 2: Model Download/Loading Issues

**Symptoms:**
- Import errors for OpenVoice V2
- Missing model files
- Initialization failures

**Solution:**
```bash
cd backend
python download_openvoice_v2.py
```

This will:
- Download OpenVoice V2 models (~500MB)
- Install required dependencies
- Set up the model directory structure

### Issue 3: Audio Format Issues

**Supported Formats:**
- WAV (recommended)
- MP3
- M4A
- FLAC

**Audio Requirements:**
- Duration: 3-300 seconds
- Sample rate: 16kHz-48kHz (auto-converted)
- Mono or stereo (auto-converted to mono)

### Issue 4: Language Support

**Supported Languages:**
- English (American, British, Indian, Australian)
- Spanish
- French
- Chinese
- Japanese
- Korean

**Usage:**
- Select language in the TTS interface
- OpenVoice V2 handles cross-lingual voice cloning automatically

## 🔧 Diagnostic Tools

### System Diagnostics
```bash
cd backend
python diagnose_voice_cloning.py
```
This will check:
- Required packages
- File permissions
- Audio processing
- OpenVoice V2 model files
- System functionality

## 🎯 OpenVoice V2 Advantages

**Pros:**
- ✅ Higher quality voice matching
- ✅ 6 languages with native support
- ✅ Two-stage architecture for better quality
- ✅ Better emotion preservation
- ✅ Zero-shot cross-lingual cloning
- ✅ MIT Licensed - free for commercial use
- ✅ Actively maintained

**Requirements:**
- Python 3.8+
- ~1-2GB RAM
- ~500MB storage for models
- Optional: CUDA GPU for faster processing

## 🛠️ Step-by-Step Setup Guide

### Initial Setup:

1. **Install Dependencies:**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Download Models:**
   ```bash
   python download_openvoice_v2.py
   ```

3. **Run Diagnostics:**
   ```bash
   python diagnose_voice_cloning.py
   ```

4. **Start Backend:**
   ```bash
   python main.py
   ```

5. **Test with Simple Audio:**
   - Use a clear WAV file
   - 10-30 seconds duration
   - Good audio quality

### If Issues Persist:

1. **Check Logs:** Look for specific error messages in the backend console
2. **Run Diagnostics:** Use the diagnostic script to identify issues
3. **Check Audio Files:** Ensure your audio files are valid and supported
4. **Verify Models:** Ensure OpenVoice V2 models are downloaded correctly

## 🔄 Quick Recovery Commands

```bash
# Full setup from scratch
cd backend
pip install -r requirements.txt
python download_openvoice_v2.py
python diagnose_voice_cloning.py
python main.py

# Just run diagnostics
python diagnose_voice_cloning.py

# Re-download models if corrupted
python download_openvoice_v2.py
```

## 📞 Getting Help

If issues persist:

1. **Check Logs:** Look for specific error messages
2. **Run Diagnostics:** Use the diagnostic script
3. **Verify Setup:** Ensure all steps were completed
4. **Check Audio:** Use supported formats and quality

## 🚀 Performance Tips

1. **Use GPU:** Install CUDA for faster processing
2. **Audio Quality:** Use high-quality reference audio
3. **Duration:** 10-30 seconds is optimal for voice cloning
4. **Format:** WAV files work best
5. **Languages:** Use native language support when possible
