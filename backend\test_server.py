#!/usr/bin/env python3
"""
Test script to debug server startup issues
"""

import sys
import os

print("🔍 Testing server startup...")
print(f"📁 Current directory: {os.getcwd()}")
print(f"🐍 Python version: {sys.version}")
print(f"📦 Python executable: {sys.executable}")

# Test imports
try:
    print("\n📦 Testing imports...")
    
    print("  - Importing FastAPI...")
    from fastapi import FastAPI
    print("  ✅ FastAPI imported")
    
    print("  - Importing uvicorn...")
    import uvicorn
    print("  ✅ uvicorn imported")
    
    print("  - Importing OpenVoice V2 cloning...")
    from openvoice_v2_cloning import OpenVoiceV2VoiceCloning
    print("  ✅ OpenVoice V2 cloning imported")
    
    print("\n✅ All imports successful!")
    
except Exception as e:
    print(f"\n❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Test voice cloning system initialization
try:
    print("\n🚀 Testing OpenVoice V2 initialization...")
    voice_system = OpenVoiceV2VoiceCloning()
    print("✅ OpenVoice V2 initialized successfully!")
    
except Exception as e:
    print(f"\n❌ Initialization error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Test basic FastAPI app
try:
    print("\n🌐 Testing FastAPI app creation...")
    app = FastAPI()
    
    @app.get("/test")
    def test():
        return {"status": "ok"}
    
    print("✅ FastAPI app created successfully!")
    
    # Try to start the server
    print("\n🚀 Starting test server on port 8001...")
    uvicorn.run(app, host="0.0.0.0", port=8001)
    
except Exception as e:
    print(f"\n❌ Server error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
